import { useState, useEffect, useCallback } from 'react';

interface MessageDraft {
  id: string;
  content: string;
  timestamp: number;
  conversationId?: string;
  type: 'text' | 'voice';
  metadata?: {
    voiceData?: Blob;
    duration?: number;
    language?: string;
  };
}

interface OfflineMessage {
  id: string;
  content: string;
  timestamp: number;
  conversationId?: string;
  type: 'text' | 'voice';
  status: 'pending' | 'sending' | 'failed' | 'sent';
  retryCount: number;
  metadata?: any;
}

const STORAGE_KEYS = {
  DRAFTS: 'catchup_message_drafts',
  OFFLINE_MESSAGES: 'catchup_offline_messages',
  SETTINGS: 'catchup_offline_settings',
};

interface OfflineSettings {
  autoSave: boolean;
  autoSaveInterval: number; // ms
  maxDrafts: number;
  maxOfflineMessages: number;
  retryAttempts: number;
  retryDelay: number; // ms
}

const DEFAULT_SETTINGS: OfflineSettings = {
  autoSave: true,
  autoSaveInterval: 2000,
  maxDrafts: 10,
  maxOfflineMessages: 50,
  retryAttempts: 3,
  retryDelay: 5000,
};

export const useOfflineMessageDrafts = (conversationId?: string) => {
  const [drafts, setDrafts] = useState<MessageDraft[]>([]);
  const [offlineMessages, setOfflineMessages] = useState<OfflineMessage[]>([]);
  const [settings, setSettings] = useState<OfflineSettings>(DEFAULT_SETTINGS);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [currentDraft, setCurrentDraft] = useState<string>('');

  // Load data from localStorage
  const loadFromStorage = useCallback(() => {
    try {
      const storedDrafts = localStorage.getItem(STORAGE_KEYS.DRAFTS);
      const storedOfflineMessages = localStorage.getItem(STORAGE_KEYS.OFFLINE_MESSAGES);
      const storedSettings = localStorage.getItem(STORAGE_KEYS.SETTINGS);

      if (storedDrafts) {
        setDrafts(JSON.parse(storedDrafts));
      }

      if (storedOfflineMessages) {
        setOfflineMessages(JSON.parse(storedOfflineMessages));
      }

      if (storedSettings) {
        setSettings({ ...DEFAULT_SETTINGS, ...JSON.parse(storedSettings) });
      }
    } catch (error) {
      console.error('Error loading offline data:', error);
    }
  }, []);

  // Save data to localStorage
  const saveToStorage = useCallback((key: string, data: any) => {
    try {
      localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
      // Handle storage quota exceeded
      if (error instanceof DOMException && error.code === 22) {
        // Clear old data and try again
        clearOldData();
        try {
          localStorage.setItem(key, JSON.stringify(data));
        } catch (retryError) {
          console.error('Failed to save after cleanup:', retryError);
        }
      }
    }
  }, []);

  // Clear old data when storage is full
  const clearOldData = useCallback(() => {
    const now = Date.now();
    const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000);

    // Remove old drafts
    setDrafts(prev => {
      const filtered = prev.filter(draft => draft.timestamp > oneWeekAgo);
      saveToStorage(STORAGE_KEYS.DRAFTS, filtered);
      return filtered;
    });

    // Remove old offline messages
    setOfflineMessages(prev => {
      const filtered = prev.filter(msg => 
        msg.timestamp > oneWeekAgo && msg.status !== 'sent'
      );
      saveToStorage(STORAGE_KEYS.OFFLINE_MESSAGES, filtered);
      return filtered;
    });
  }, [saveToStorage]);

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Load data on mount
  useEffect(() => {
    loadFromStorage();
  }, [loadFromStorage]);

  // Auto-save current draft
  useEffect(() => {
    if (!settings.autoSave || !currentDraft.trim()) return;

    const timeoutId = setTimeout(() => {
      saveDraft(currentDraft);
    }, settings.autoSaveInterval);

    return () => clearTimeout(timeoutId);
  }, [currentDraft, settings.autoSave, settings.autoSaveInterval]);

  // Save draft
  const saveDraft = useCallback((content: string, type: 'text' | 'voice' = 'text', metadata?: any) => {
    if (!content.trim()) return;

    const draft: MessageDraft = {
      id: `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      timestamp: Date.now(),
      conversationId,
      type,
      metadata,
    };

    setDrafts(prev => {
      const filtered = prev.filter(d => d.conversationId !== conversationId);
      const newDrafts = [draft, ...filtered].slice(0, settings.maxDrafts);
      saveToStorage(STORAGE_KEYS.DRAFTS, newDrafts);
      return newDrafts;
    });

    return draft.id;
  }, [conversationId, settings.maxDrafts, saveToStorage]);

  // Load draft
  const loadDraft = useCallback((draftId?: string) => {
    if (draftId) {
      const draft = drafts.find(d => d.id === draftId);
      return draft;
    }

    // Load most recent draft for current conversation
    const conversationDrafts = drafts.filter(d => d.conversationId === conversationId);
    return conversationDrafts[0] || null;
  }, [drafts, conversationId]);

  // Delete draft
  const deleteDraft = useCallback((draftId: string) => {
    setDrafts(prev => {
      const filtered = prev.filter(d => d.id !== draftId);
      saveToStorage(STORAGE_KEYS.DRAFTS, filtered);
      return filtered;
    });
  }, [saveToStorage]);

  // Queue message for offline sending
  const queueOfflineMessage = useCallback((content: string, type: 'text' | 'voice' = 'text', metadata?: any) => {
    const message: OfflineMessage = {
      id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      timestamp: Date.now(),
      conversationId,
      type,
      status: 'pending',
      retryCount: 0,
      metadata,
    };

    setOfflineMessages(prev => {
      const newMessages = [message, ...prev].slice(0, settings.maxOfflineMessages);
      saveToStorage(STORAGE_KEYS.OFFLINE_MESSAGES, newMessages);
      return newMessages;
    });

    return message.id;
  }, [conversationId, settings.maxOfflineMessages, saveToStorage]);

  // Send offline messages when online
  const sendOfflineMessages = useCallback(async (sendFunction: (message: OfflineMessage) => Promise<boolean>) => {
    if (!isOnline) return;

    const pendingMessages = offlineMessages.filter(msg => 
      msg.status === 'pending' || msg.status === 'failed'
    );

    for (const message of pendingMessages) {
      try {
        // Update status to sending
        setOfflineMessages(prev => 
          prev.map(msg => 
            msg.id === message.id ? { ...msg, status: 'sending' as const } : msg
          )
        );

        const success = await sendFunction(message);

        if (success) {
          // Mark as sent
          setOfflineMessages(prev => {
            const updated = prev.map(msg => 
              msg.id === message.id ? { ...msg, status: 'sent' as const } : msg
            );
            saveToStorage(STORAGE_KEYS.OFFLINE_MESSAGES, updated);
            return updated;
          });
        } else {
          throw new Error('Send function returned false');
        }
      } catch (error) {
        console.error('Failed to send offline message:', error);
        
        // Update retry count and status
        setOfflineMessages(prev => {
          const updated = prev.map(msg => {
            if (msg.id === message.id) {
              const newRetryCount = msg.retryCount + 1;
              return {
                ...msg,
                retryCount: newRetryCount,
                status: newRetryCount >= settings.retryAttempts ? 'failed' as const : 'pending' as const,
              };
            }
            return msg;
          });
          saveToStorage(STORAGE_KEYS.OFFLINE_MESSAGES, updated);
          return updated;
        });

        // Wait before next retry
        if (message.retryCount < settings.retryAttempts - 1) {
          await new Promise(resolve => setTimeout(resolve, settings.retryDelay));
        }
      }
    }
  }, [isOnline, offlineMessages, settings.retryAttempts, settings.retryDelay, saveToStorage]);

  // Get conversation drafts
  const getConversationDrafts = useCallback((convId?: string) => {
    const targetConversationId = convId || conversationId;
    return drafts.filter(d => d.conversationId === targetConversationId);
  }, [drafts, conversationId]);

  // Get pending offline messages
  const getPendingMessages = useCallback(() => {
    return offlineMessages.filter(msg => msg.status === 'pending' || msg.status === 'failed');
  }, [offlineMessages]);

  // Update settings
  const updateSettings = useCallback((newSettings: Partial<OfflineSettings>) => {
    setSettings(prev => {
      const updated = { ...prev, ...newSettings };
      saveToStorage(STORAGE_KEYS.SETTINGS, updated);
      return updated;
    });
  }, [saveToStorage]);

  // Clear all data
  const clearAllData = useCallback(() => {
    setDrafts([]);
    setOfflineMessages([]);
    localStorage.removeItem(STORAGE_KEYS.DRAFTS);
    localStorage.removeItem(STORAGE_KEYS.OFFLINE_MESSAGES);
  }, []);

  // Get storage usage
  const getStorageUsage = useCallback(() => {
    try {
      const draftsSize = new Blob([localStorage.getItem(STORAGE_KEYS.DRAFTS) || '']).size;
      const messagesSize = new Blob([localStorage.getItem(STORAGE_KEYS.OFFLINE_MESSAGES) || '']).size;
      const settingsSize = new Blob([localStorage.getItem(STORAGE_KEYS.SETTINGS) || '']).size;
      
      return {
        drafts: draftsSize,
        messages: messagesSize,
        settings: settingsSize,
        total: draftsSize + messagesSize + settingsSize,
      };
    } catch (error) {
      return { drafts: 0, messages: 0, settings: 0, total: 0 };
    }
  }, []);

  return {
    // State
    drafts,
    offlineMessages,
    settings,
    isOnline,
    currentDraft,
    setCurrentDraft,

    // Draft management
    saveDraft,
    loadDraft,
    deleteDraft,
    getConversationDrafts,

    // Offline message management
    queueOfflineMessage,
    sendOfflineMessages,
    getPendingMessages,

    // Settings
    updateSettings,

    // Utilities
    clearAllData,
    getStorageUsage,
    clearOldData,
  };
};
