import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { Composio } from "https://esm.sh/@composio/core@0.1.39"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ComposioRequest {
  action: 'connect' | 'callback' | 'fetch_emails' | 'send_email' | 'disconnect'
  userId?: string
  authConfigId?: string
  code?: string
  state?: string
  connectionId?: string
  to?: string
  subject?: string
  body?: string
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Get auth header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    // Verify user
    const { data: { user }, error: authError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''))
    if (authError || !user) {
      throw new Error('Invalid user token')
    }

    const { action, ...params }: ComposioRequest = await req.json()

    // Initialize Composio
    const composioApiKey = Deno.env.get('COMPOSIO_API_KEY')
    if (!composioApiKey) {
      throw new Error('Missing COMPOSIO_API_KEY')
    }

    const composio = new Composio({ apiKey: composioApiKey })

    let result

    switch (action) {
      case 'connect': {
        const { userId, authConfigId } = params
        
        // Initiate OAuth flow
        const response = await composio.connectedAccounts.initiate(
          userId || user.id,
          authConfigId || "ac_UvsrdfxJ2h2X" // Gmail auth config ID
        )

        result = {
          success: true,
          data: {
            connectionId: response.id,
            redirectUrl: response.redirectUrl
          }
        }
        break
      }

      case 'callback': {
        const { connectionId, code, state } = params
        
        // Wait for connection to complete
        try {
          const connectedAccount = await composio.connectedAccounts.get(connectionId!)
          
          // Store in database
          const { error: dbError } = await supabase
            .from('integration_authorizations')
            .upsert({
              user_id: user.id,
              business_id: '', // Required field
              provider: 'gmail',
              integration_id: connectionId,
              access_token: '', // Managed by Composio
              refresh_token: '',
              expires_at: null,
              is_active: true,
              metadata: {
                email: connectedAccount.connectionParams?.scope || '',
                scopes: ['https://www.googleapis.com/auth/gmail.readonly', 'https://www.googleapis.com/auth/gmail.send']
              }
            }, {
              onConflict: 'user_id,provider'
            })

          if (dbError) throw dbError

          result = {
            success: true,
            data: { connected: true }
          }
        } catch (error) {
          throw new Error(`Callback failed: ${error.message}`)
        }
        break
      }

      case 'fetch_emails': {
        const { connectionId } = params
        
        try {
          // Get connected account
          const connectedAccount = await composio.connectedAccounts.get(connectionId!)
          
          // Use the connected account to fetch emails via Gmail API
          // For now, return mock data - you'd implement actual Gmail API calls here
          const emails = [
            {
              id: 'demo_email_1',
              sender: '<EMAIL>',
              subject: 'Benvenuto in Gmail!',
              preview: 'Grazie per aver connesso il tuo account Gmail con CatchUp...',
              time: new Date().toLocaleDateString('it-IT'),
              isRead: false,
              isStarred: false,
              body: 'Messaggio di benvenuto completo qui...'
            }
          ]

          result = {
            success: true,
            data: { emails }
          }
        } catch (error) {
          throw new Error(`Failed to fetch emails: ${error.message}`)
        }
        break
      }

      case 'send_email': {
        const { connectionId, to, subject, body } = params
        
        try {
          // Get connected account
          const connectedAccount = await composio.connectedAccounts.get(connectionId!)
          
          // Send email via Gmail API through Composio
          // For now, simulate successful send
          console.log(`Simulated sending email to: ${to}, subject: ${subject}`)

          result = {
            success: true,
            data: { messageId: `msg_${Date.now()}` }
          }
        } catch (error) {
          throw new Error(`Failed to send email: ${error.message}`)
        }
        break
      }

      case 'disconnect': {
        const { connectionId } = params
        
        try {
          // Deactivate in database
          const { error: dbError } = await supabase
            .from('integration_authorizations')
            .update({ is_active: false })
            .eq('user_id', user.id)
            .eq('provider', 'gmail')

          if (dbError) throw dbError

          result = {
            success: true,
            data: { disconnected: true }
          }
        } catch (error) {
          throw new Error(`Failed to disconnect: ${error.message}`)
        }
        break
      }

      default:
        throw new Error(`Unknown action: ${action}`)
    }

    return new Response(
      JSON.stringify(result),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Composio Gmail error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})