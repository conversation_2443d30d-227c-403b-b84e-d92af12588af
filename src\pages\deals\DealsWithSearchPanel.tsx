import { useState } from "react";
import { useNavigate } from "react-router-dom";
import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";
import SearchPanel from "@/components/search/ModernSearchPanel";
import { useSearchPanel } from "@/hooks/useSearchPanel";
import { Map as MapIcon, List } from "lucide-react";
import { useAuth } from "@/hooks/auth/useAuth";
import { MapView } from "@/components/map/MapView";
import { NearestBusinesses } from "@/components/map/NearestBusinesses";
import type { Database } from "@/integrations/supabase/types";
import { useBusinessMode } from "@/hooks/useBusinessMode";
import DealCard from "@/components/deals/core/DealCard";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

type Deal = Database["public"]["Tables"]["deals"]["Row"] & {
  businesses?: {
    name: string;
    address: string;
    city: string;
    zip_code: string;
    state: string;
    country: string;
    category_id: string;
    owner_id: string;
  };
};

type Business = {
  id: string;
  name: string;
  latitude: number | null;
  longitude: number | null;
  deal_count_published: number | null;
  deals?: Deal[];
  photos: string[];
  fake: boolean;
  icon: string;
  category_id: string;
};

const DealsWithSearchPanel = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [viewMode, setViewMode] = useState<"list" | "map">("map");
  const { isBusinessMode } = useBusinessMode();

  const { data: businessesData, isLoading: isBusinessesLoading } = useQuery({
    queryKey: ['businesses-with-deals', user?.id],
    queryFn: async (): Promise<{ businesses: Business[], deals: Deal[] }> => {
      // Get all businesses with deals
      const { data: allBusinessesData, error: allBusinessesError } =
        await supabase
          .from("businesses_with_counts")
          .select(
            `
          id, 
          name, 
          latitude, 
          longitude,
          deal_count_published,
          photos,
          fake,
          category_id,
          categories!inner (
            icon
          )
        `
          )
          .not("latitude", "is", null)
          .not("longitude", "is", null);

      if (allBusinessesError) {
        throw new Error(`Errore nel caricamento delle attività: ${allBusinessesError.message}`);
      }

      // Get businesses with their deals
      const { data: businessesWithDealsData, error: businessesError } =
        await supabase
          .from("businesses_with_counts")
          .select(
            `
          id, 
          name, 
          latitude, 
          longitude,
          deal_count_published,
          photos,
          fake,
          category_id,
          categories!inner (
            icon
          ),
          deals!inner (
            *,
            businesses!inner (
              name,
              address,
              city,
              zip_code,
              state,
              country,
              category_id,
              owner_id
            )
          )
        `
          )
          .not("latitude", "is", null)
          .not("longitude", "is", null);

      if (businessesError) {
        throw new Error(`Errore nel caricamento delle attività con offerte: ${businessesError.message}`);
      }

      // Transform the data
      const transformedBusinesses = allBusinessesData?.map(business => ({
        ...business,
        icon: business.categories?.icon || null
      })) || [];

      if (!businessesWithDealsData || businessesWithDealsData.length === 0) {
        return { businesses: transformedBusinesses as Business[], deals: [] };
      }

      // Process deals
      const filteredBusinesses = businessesWithDealsData
        .map((business) => ({
          ...business,
          icon: business.categories?.icon || null,
          deals: business.deals?.filter((deal) => {
            return deal && deal.title;
          }),
        }))
        .filter((business) => business.deals && business.deals.length > 0) || [];

      const allDeals = filteredBusinesses.flatMap(
        (business) => business.deals || []
      );
      
      return { businesses: transformedBusinesses as Business[], deals: allDeals as Deal[] };
    },
    enabled: !!user?.id,
    staleTime: 1000 * 60 * 2, // 2 minutes - deals data needs moderate freshness
    refetchOnWindowFocus: false,
  });

  const businesses = businessesData?.businesses || [];
  const deals = businessesData?.deals || [];
  const isLoading = isBusinessesLoading;

  // Use the SearchPanel hook
  const {
    isOpen: isSearchPanelOpen,
    filters,
    openSearchPanel,
    closeSearchPanel,
    updateFilters,
    hasActiveFilters,
    activeFiltersCount,
  } = useSearchPanel();


  // Filter deals based on search panel filters
  const filteredDeals = deals.filter((deal) => {
    if (!deal || !deal.title) return false;

    try {
      const dealTitle = deal.title || "";
      const businessName = deal.businesses?.name || "";
      const businessAddress = deal.businesses?.address || "";
      const businessCategoryId = deal.businesses?.category_id || "";

      const matchesSearch = 
        filters.searchQuery === "" || 
        dealTitle.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
        businessName.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
        businessAddress.toLowerCase().includes(filters.searchQuery.toLowerCase());

      const matchesCategory =
        !filters.selectedCategoryIds || filters.selectedCategoryIds.length === 0 ||
        (businessCategoryId && filters.selectedCategoryIds.includes(businessCategoryId));

      return matchesSearch && matchesCategory;
    } catch (error) {
      console.error("Error filtering deal:", error, deal);
      return false;
    }
  });

  // Filter businesses for map view
  const filteredBusinesses = businesses.filter((business) => {
    try {
      const businessName = business.name || "";
      const businessCategoryId = business.category_id || "";

      const matchesSearch = 
        filters.searchQuery === "" || 
        businessName.toLowerCase().includes(filters.searchQuery.toLowerCase());

      const matchesCategory =
        !filters.selectedCategoryIds || filters.selectedCategoryIds.length === 0 ||
        (businessCategoryId && filters.selectedCategoryIds.includes(businessCategoryId));

      return matchesSearch && matchesCategory;
    } catch (error) {
      console.error("Error filtering business:", error, business);
      return false;
    }
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 pb-20">
        <UnifiedHeader variant="dashboard" showGreeting={true} showDate={true} />
        <div className="p-4 pt-20">
          <div className="grid grid-cols-1 gap-4">
            {[1, 2, 3, 4].map((item) => (
              <div
                key={item}
                className="bg-gray-100 rounded-xl h-48 animate-pulse"
              />
            ))}
          </div>
        </div>
        <BottomNavigationBar isBusiness={isBusinessMode} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <UnifiedHeader 
        variant="dashboard"
        showGreeting={true}
        showDate={true}
        showSearch={true}
        onSearchClick={openSearchPanel}
        hasActiveFilters={hasActiveFilters}
        activeFiltersCount={activeFiltersCount}
      />
      
      {/* Search Panel */}
      <SearchPanel  
        isOpen={isSearchPanelOpen}
        onClose={closeSearchPanel}
        filters={filters}
        onFiltersChange={updateFilters}
        placeholder="Cerca offerte e attività"
        showLocationFilter={true}
        showBusinessFilters={isBusinessMode}
        showTextSearch={false}
        showSortOptions={false}
        onDistanceChange={()=>{}}
      />

      <main className="pt-16 pb-20 px-4">
        {/* View Mode Toggle Button */}
        <button
          onClick={() =>
            setViewMode((prev) => (prev === "list" ? "map" : "list"))
          }
          className="fixed right-4 top-20 z-10 bg-brand-primary shadow-lg rounded-full px-5 py-2.5 hover:bg-brand-primary/90 transition-colors"
        >
          {viewMode === "list" ? (
            <MapIcon className="h-6 w-6 text-white" />
          ) : (
            <List className="h-6 w-6 text-white" />
          )}
        </button>

        {viewMode === "list" ? (
          <>
            {/* Nearest Businesses Component */}
            <NearestBusinesses />
            
            {/* Results */}
            <div className="mt-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">
                  Offerte Trovate ({filteredDeals.length})
                </h2>
                {filters.searchQuery && (
                  <p className="text-sm text-gray-600">
                    Risultati per: "{filters.searchQuery}"
                  </p>
                )}
              </div>
              
              <div className="grid grid-cols-1 gap-4">
                {filteredDeals.map((deal) => (
                  <DealCard
                    key={deal.id}
                    deal={deal}
                    variant="full"
                    onClick={() => navigate(`/deal/${deal.id}`)}
                    showVisitBusiness={true}
                  />
                ))}
                {filteredDeals.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-gray-500">Nessuna offerta trovata</p>
                    <p className="text-sm text-gray-400 mt-2">
                      Prova a modificare i filtri di ricerca
                    </p>
                  </div>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="mt-2 relative" style={{ height: "calc(100vh - 120px)" }}>
            {/* Results count overlay for map view */}
            {filteredBusinesses.length > 0 && (
              <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10 bg-white shadow-lg rounded-full px-4 py-2">
                <span className="text-sm font-medium">
                  {filteredBusinesses.length} attività trovate
                </span>
              </div>
            )}

            <MapView
              businesses={filteredBusinesses}
              onDealClick={(dealId) => navigate(`/deal/${dealId}`)}
            />
          </div>
        )}
      </main>
      
      <BottomNavigationBar isBusiness={isBusinessMode} />
    </div>
  );
};

export default DealsWithSearchPanel;  