
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowRight, Navigation, Ticket } from "lucide-react";
import { toast } from "sonner";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";
import { useNearestBusinesses } from "@/hooks/useNearestBusinesses";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export function NearestBusinesses() {
  const navigate = useNavigate();
  const { userLocation } = useLocationManagement();
  const { businesses, loading, error } = useNearestBusinesses({
    maxDistance_meters: 5000,
    require_deals: true,
  });

  const businessIds = businesses.map(business => business.id);
  const { data: businessDeals = {} } = useQuery({
    queryKey: ['business-deals', businessIds],
    queryFn: async (): Promise<Record<string, { id: string }[]>> => {
      if (businessIds.length === 0) return {};
      
      const { data, error } = await supabase
        .from('deals')
        .select('id, business_id')
        .in('business_id', businessIds)
        .eq('status', 'published');
        
      if (error) {
        throw new Error(`Errore nel caricamento delle offerte: ${error.message}`);
      }
      
      // Organizza le offerte per business_id
      const dealsMap: Record<string, { id: string }[]> = {};
      data.forEach((deal: any) => {
        if (!dealsMap[deal.business_id]) {
          dealsMap[deal.business_id] = [];
        }
        dealsMap[deal.business_id].push({ id: deal.id });
      });
      
      return dealsMap;
    },
    enabled: businessIds.length > 0,
    staleTime: 1000 * 60 * 2, // 2 minutes - deals data needs moderate freshness
    refetchOnWindowFocus: false,
  });

  const handleBusinessClick = (businessId: string) => {
    // Verifica se ci sono offerte per questa attività
    if (businessDeals[businessId] && businessDeals[businessId].length > 0) {
      // Reindirizza alla prima offerta disponibile
      navigate(`/deal/${businessDeals[businessId][0].id}`);
    } else {
      // Se non ci sono offerte, vai alla pagina dell'attività (fallback)
      navigate(`/business/${businessId}`);
    }
  };

  if (loading) {
    return (
      <div className="mt-4 relative overflow-hidden rounded-xl bg-gray-50 p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-md font-semibold">Attività vicine con offerte</h3>
        </div>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="flex items-center gap-3 animate-pulse"
            >
              <div className="h-12 w-12 rounded-lg bg-gray-200"></div>
              <div className="space-y-2 flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="h-8 w-8 rounded-full bg-gray-200"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-4 rounded-xl bg-red-50 p-4">
        <p className="text-red-600">Errore nel caricamento delle attività vicine</p>
      </div>
    );
  }

  if (businesses.length === 0) {
    return (
      <div className="mt-4 rounded-xl bg-gray-50 p-4">
        <p className="text-gray-500 text-center">Nessuna attività con offerte trovata nelle vicinanze</p>
      </div>
    );
  }

  return (
    <div className="mt-4 rounded-xl bg-gray-50 p-4">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-md font-semibold">Attività vicine con offerte</h3>
        <Button
          variant="ghost"
          size="sm"
          className="text-brand-primary"
          onClick={() => navigate("/map")}
        >
          Mappa
          <ArrowRight className="ml-1 h-4 w-4" />
        </Button>
      </div>
      <div className="space-y-3">
        {businesses.map((business) => (
          <div
            key={business.id}
            className="flex items-center gap-3 cursor-pointer p-2 rounded-lg hover:bg-gray-100 transition-colors"
            onClick={() => handleBusinessClick(business.id)}
          >
            {/* Business thumbnail */}
            <div className="h-12 w-12 rounded-lg overflow-hidden bg-gray-200 flex-shrink-0">
              {business.photos && business.photos.length > 0 ? (
                <img 
                  src={business.photos[0]} 
                  alt={business.name} 
                  className="h-full w-full object-cover" 
                />
              ) : (
                <div className="h-full w-full bg-gradient-to-r from-brand-primary to-brand-secondary flex items-center justify-center text-white">
                  <Ticket className="h-5 w-5" />
                </div>
              )}
            </div>
            
            <div className="flex-1">
              <p className="font-medium text-gray-800">{business.name}</p>
              <div className="flex items-center gap-1.5">
                <p className="text-xs text-gray-500">{business.distanceText}</p>
                <span className="text-xs text-brand-primary font-semibold">
                  • {business.deal_count} {business.deal_count === 1 ? 'offerta' : 'offerte'}
                </span>
              </div>
            </div>
            <Button
              size="icon"
              variant="ghost"
              className="h-8 w-8 rounded-full hover:bg-brand-light hover:text-brand-primary"
              onClick={(e) => {
                e.stopPropagation();
                if (business.latitude && business.longitude) {
                  window.open(
                    `https://www.google.com/maps/dir/?api=1&destination=${business.latitude},${business.longitude}`,
                    "_blank"
                  );
                } else {
                  toast.error("Coordinate non disponibili per questa attività");
                }
              }}
            >
              <Navigation className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
}
