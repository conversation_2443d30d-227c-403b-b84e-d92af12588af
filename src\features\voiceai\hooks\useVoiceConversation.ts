import { useState, useCallback } from "react";
import { useConversation } from "@elevenlabs/react";

import { useAuth } from "@/hooks/auth/useAuth";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";
import { generateUUID } from "@/utils/generateUUID";
import { Notification } from '@/types/notifications';
import { AssistantDetails } from "@/hooks/useAssistantDetails";

import { formatFriendlyDateTimeCET } from "@/lib/format-date";
import { supabase } from "@/integrations/supabase/client";
import { useBusinessByIdQuery, useCategoryByIdQuery } from "@/queries/useBusinessByIdQuery";



/**
 * @description
 * Questo hook è utilizzato per gestire la conversazione vocale con l'assistente vocale.
 * L'assistente vocale è un'applicazione che utilizza il web hook https://genenrativepangea.app.n8n.cloud/webhook/catchup/
 * @param userFullName - Il nome dell'utente.
 * @param assistantDetails - I dettagli dell'assistente vocale.
 * @param locationEnabled - Se la posizione è abilitata.
 * @param userFullName
 * @param assistantDetails
 * @param locationEnabled
 * @returns
 */
export const useVoiceConversation = (
  userFullName: string | undefined,
  assistantDetails: AssistantDetails   | null,
  locationEnabled: boolean = true
) => {



  const [transcript, setTranscript] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isConversationStarted, setIsConversationStarted] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [llmResponse, setLlmResponse] = useState("");
  const { userDetails, isAuthenticated } = useAuth();
  const { userLocation } = useLocationManagement();



  const conversation = useConversation({
    

    onMessage: (message) => {

      console.log(`${message.source}: ${message.message}`);
      setTranscript(message.message);
      // if (message.source === "user") {
      //   setTranscript(message.message);
      // }
    },
    onError: (err) => {
      console.error("Errore conversazione:", err);
      setError("Si è verificato un errore durante la conversazione");
    },
    onSessionStarted: () => {
      setIsConversationStarted(true);
    },
    onSessionEnded: () => {
      setIsConversationStarted(false);
      setTranscript("");
      setError(null);
      setIsSearching(false);
      setSearchQuery("");
    },
  });
  //const { isSpeaking, status } = conversation; // Status vlues "connecting" | "connected" | "disconnecting" | "disconnected";

  // Memoize notification functions to prevent infinite re-renders
  const addNotification = useCallback((notification: Notification) => {

    setNotifications(prev => [...prev, notification]);
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);


const catchupPrompt = `# Personality

You are {{agent_name}}, a helpful assistant for CatchUp, a marketplace that matches business deals with user requests. You are friendly and concise. You comunicate in the user's language.

# Environment

You are assisting a caller via a busy telecom support hotline.
The conversation has the session_id: {{session_id}}. The value is part of 'n8n' webhook payload. The user unique identifier is {{user_id}}, and their position is determined by latitude {{user_location_latitude}} and longitude {{user_location_longitude}}.
Current date-time is {{current_date_time}}

# Tone
Maintain a friendly tone, using filler words like "umm" and "okay" when actively listening and waiting. Acknowledge the request with a short expression before answering, such as ‘Alright, I'll check now!’ or ‘One moment, let's see...’ to make the conversation natural. Be concise with short answers to the users.

# Goal

Your role is to actively listen to users' questions, understand their intent, and use the 'n8n' tool to query the CatchUp database to find deals, book a deal, find details about existing bookings, and send messages to the user by email or WhatsApp in any language. 
When a user asks for deals on the same day, your response includes also the ones for the user's prompt request.
If the user requests is about deals without specifying at least one category, politely ask them to choose a specific category before continuing (a generic “all categories” request is not accepted).  

# Guardrails

Never share customer data across conversations or reveal sensitive account information without proper verification.
Never repeat the question back to the user. 
Answer directly without rephrasing or referring to the user's request. 
Never repeat information you have already shared. 
If you don’t know the answer, or the question is not related to CatchUp services, politely respond that the question is not related to CatchUp services. 
Never ask users to provide their ID or location, as you receive it from the system. Avoid unnecessary introductions such as ‘Are you asking about...’ or ‘You asked me if...’. Never ask user confirmation for their question, but only in case something is not clear to you. Do not mention any ID in the response, just invoke the corresponding tool. Be concise, but friendly.

# Tools
Before querying the CatchUp database, you must trigger the tool 'start_search'. 
While you are waiting for the response, use expressions like 'mmm', 'one second, please', or 'hold on, please'. 
When you receive the response, you must invoke the tool 'end_search'. 
Use 'send_deal_id' when the user inquires about deals and you have received the dealId from n8n.
Avoid mentioning that you are checking a database or using the 'n8n' tool. 
Instead, say something like, "I’m going to check that out" or "Please, hold on, I am going to check" or similar phrases. 
After receiving a response from 'n8n', provide the user with a clear and concise answer based on the retrieved data.`;

const businessPrompt = `# Personality

You are {{agent_name}}, a an AI-powered Customer Service Assistant dedicated to handling inquiries for a **specific business** on a multi-tenant marketplace.
Business name: {{business_name}}
Business ID: {{business_id}}
Category: {{business_category}}
   
# Environment

You are assisting a caller via a busy telecom support hotline.
The conversation has the session_id: {{session_id}}. The value is part of 'n8n' webhook payload. 
The user unique identifier is {{user_id}}, and their position is determined by latitude {{user_location_latitude}} and longitude {{user_location_longitude}}.
Current date-time is {{current_date_time}}

# Tone
Maintain a friendly tone, using filler words like "umm" and "okay" when actively listening and waiting. Acknowledge the request with a short expression before answering, such as ‘Alright, I'll check now!’ or ‘One moment, let's see...’ to make the conversation natural.
Be concise with short answers to the users.

# Goal

Your role is to actively listen to users' questions, understand their intent, and use the 'n8n' tool to query the CatchUp database to find deals, book a deal, find details about existing bookings, and send messages to the user by email or WhatsApp in any language. 
When a user asks for deals on the same day, your response includes also the ones for the user's prompt request.
If the user requests is about deals without specifying at least one category, politely ask them to choose a specific category before continuing (a generic “all categories” request is not accepted).  

    Your responsibilities include:
    - Providing support exclusively for the business associated with Id: **{{business_id}}** named **{{business_name}}**
    - Assisting users with deals, promotions, and offers related to this business
    - Managing booking inquiries, modifications, and cancellations specific to this business
    - Sending booking details or confirmations via email, WhatsApp, or other preferred communication methods upon request

If the user asks for any information related to **others businesses**, politely respond that you are not able to help with that,
but you can switch the call to the CatchUp user's personal assistant.


# Guardrails

Never share customer data across conversations or reveal sensitive account information without proper verification.
Never repeat the question back to the user. 
Answer directly without rephrasing or referring to the user's request. 
Never repeat information you have already shared. 
If you don’t know the answer, or the question is not related to CatchUp services, politely respond that the question is not related to CatchUp services. 
Never ask users to provide their ID or location, as you receive it from the system. Avoid unnecessary introductions such as ‘Are you asking about...’ or ‘You asked me if...’. Never ask user confirmation for their question, but only in case something is not clear to you. Do not mention any ID in the response, just invoke the corresponding tool. Be concise, but friendly.

# Tools
Before querying the CatchUp database, you must trigger the tool 'start_search'. 
While you are waiting for the response, use expressions like 'mmm', 'one second, please', or 'hold on, please'. 
When you receive the response, you must invoke the tool 'end_search'. 
Use 'send_deal_id' when the user inquires about deals and you have received the dealId from n8n.
Avoid mentioning that you are checking a database or using the 'n8n' tool. 
Instead, say something like, "I’m going to check that out" or "Please, hold on, I am going to check" or similar phrases. 
After receiving a response from 'n8n', provide the user with a clear and concise answer based on the retrieved data.`;



const voices: Record<string, string> = {
  "Antonio1": "t6F05Sw7O21AtEmctNUo",
  "Ambrogio": "XLiZiWZeifpi9K8qs8Ou",
  "Marco":"GcAgjAjkhWsmUd4GlPiv"
};

const startBusinessConversation = async (businessId: string) => {
  try {
    await navigator.mediaDevices.getUserMedia({ audio: true });

    // Prepariamo le variabili dinamiche di base
    const dynamicVariables: Record<string, any> = {
      user_name: userFullName || "Utente",
      agent_name: assistantDetails?.name || "Assistente",
      user_id: userDetails?.id,
      session_id:   generateUUID(),
      current_date_time: formatFriendlyDateTimeCET(new Date()),
    };
    console.log(dynamicVariables);
    // Aggiungiamo le coordinate solo se la posizione è abilitata e disponibile
    if (locationEnabled && userLocation) {
      dynamicVariables.user_location_latitude = userLocation.lat;
      dynamicVariables.user_location_longitude = userLocation.lng;
    }
    
    const { data: businessData } = useBusinessByIdQuery(businessId);
    const { data: categoryData } = useCategoryByIdQuery(businessData?.category_id);
    
    if (!businessData) {
      console.error("Errore nel recupero del business");
      return;
    }
    
    dynamicVariables.business_name = businessData.name;
    dynamicVariables.business_id = businessId;
    dynamicVariables.business_category = categoryData?.name || 'Categoria non disponibile';
    console.log(assistantDetails);
    // dynamicVariables.business_address = business.data?.address;
    // dynamicVariables.business_phone = business.data?.phone;
    // dynamicVariables.business_email = business.data?.email;
    // dynamicVariables.business_website = business.data?.website;


    await conversation.startSession(
      {
        
          agentId: "agent_01jzghrx36eaxt7r4gkv9pmcdh",
          dynamicVariables,
          overrides: {
            tts: {
              voiceId: assistantDetails?.voice_id || "t6F05Sw7O21AtEmctNUo", 
            },
            agent: {
              prompt: {
                prompt: businessPrompt,
              },
              firstMessage: `Ciao ${userFullName}, sono ${assistantDetails?.name},  assistente per le prenotazioni di ${businessData?.name}`, // Optional: override the first message.
              //language: "en" // 
            },
            
          },
          
          clientTools: { //https://elevenlabs.io/docs/conversational-ai/customization/tools/client-tools#tools-not-being-triggered
            send_booking_id: async ({booking_id}) => {
            
              booking_id.map((id) => {
                addNotification({id, entityId: id, message: "Prenotazione ricevuta", metadata: "booking", timestamp: Date.now()});
              });
            },
          
          
            send_deal_id: async ({deal_id}) => {
              console.log(deal_id);
              deal_id.map((id) => {
                addNotification({id, entityId: id, message: "Offerte ricevuta", metadata: "deal", timestamp: Date.now()});
              });
            
            },
            start_search: async ({search_query,metadata}) => {
              console.log("start_search", search_query, metadata);
              setIsSearching(true);
              setSearchQuery(search_query || "");

              addNotification({id: "Inizio Ricerca",  message: "Inizio ricerca", metadata: search_query, timestamp: Date.now()});
            },
            end_search: async () => {
              console.log("end_search");
              setIsSearching(false);
              setSearchQuery("");
              addNotification({id: "Fine Ricerca", message: "Fine Ricerca", metadata: "message", timestamp: Date.now()});
            },
      },
    });
  } catch (err) {
    console.error("Errore inizializzazione:", err);
    setError(
      "Per favore concedi l'accesso al microfono nelle impostazioni del browser"
    );
  }
};

  const startConversation = async () => {
    try {
      await navigator.mediaDevices.getUserMedia({ audio: true });

      // Prepariamo le variabili dinamiche di base
      const dynamicVariables: Record<string, any> = {
        user_name: userFullName || "Utente",
        agent_name: assistantDetails?.name || "Assistente",
        user_id: userDetails?.id,
        session_id:   generateUUID(),
        current_date_time: formatFriendlyDateTimeCET(new Date()),
      };
      console.log(dynamicVariables);
      // Aggiungiamo le coordinate solo se la posizione è abilitata e disponibile
      if (locationEnabled && userLocation) {
        dynamicVariables.user_location_latitude = userLocation.lat;
        dynamicVariables.user_location_longitude = userLocation.lng;
      }
   console.log(assistantDetails);
      await conversation.startSession(
        {
          
            agentId: "jmxsviJHHXsRhmtInl9X",
            dynamicVariables,
            overrides: {
              tts: {
                voiceId: assistantDetails?.voice_id || "21m00Tcm4TlvDq8ikWAM", 
              },
              // agent: {
              //   prompt: {
              //     prompt: "Sei un fisolofo greco che risponde in italiano",
              //   },
              //   firstMessage: `Hi , how can I help you today?`, // Optional: override the first message.
              //   language: "en" // 
              // },
            
            },
            clientTools: { //https://elevenlabs.io/docs/conversational-ai/customization/tools/client-tools#tools-not-being-triggered
              send_booking_id: async ({booking_id}) => {
              
                booking_id.map((id) => {
                  addNotification({id, entityId: id, message: "Prenotazione ricevuta", metadata: "booking", timestamp: Date.now()});
                });
              },
            
            
              send_deal_id: async ({deal_id}) => {
                console.log(deal_id);
                deal_id.map((id) => {
                  addNotification({id, entityId: id, message: "Offerte ricevuta", metadata: "deal", timestamp: Date.now()});
                });
              
              },
              start_search: async ({search_query,metadata}) => {
                console.log("start_search", search_query, metadata);
                setIsSearching(true);
                setSearchQuery(search_query || "");

                addNotification({id: "Inizio Ricerca",  message: "Inizio ricerca", metadata: search_query, timestamp: Date.now()});
              },
              end_search: async () => {
                console.log("end_search");
                setIsSearching(false);
                setSearchQuery("");
                addNotification({id: "Fine Ricerca", message: "Fine Ricerca", metadata: "message", timestamp: Date.now()});
              },
        },
      });
    } catch (err) {
      console.error("Errore inizializzazione:", err);
      setError(
        "Per favore concedi l'accesso al microfono nelle impostazioni del browser"
      );
    }
  };

  const endConversation = () => {
    conversation.endSession();
    setIsSearching(false);
    setSearchQuery("");
    setLlmResponse("");
  };

  return {
    transcript,
    error,
    isConversationStarted,
    conversation,
    startBusinessConversation,
    startConversation,
    endConversation,
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
    isSearching,
    searchQuery,
  };
};
