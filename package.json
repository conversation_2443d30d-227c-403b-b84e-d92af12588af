{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@composio/core": "^0.1.39", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@elevenlabs/react": "^0.2.1", "@faker-js/faker": "^8.4.1", "@googlemaps/markerclusterer": "^2.5.3", "@hookform/resolvers": "^3.9.0", "@iconify/react": "^6.0.0", "@langchain/core": "^0.3.66", "@langchain/langgraph": "^0.4.0", "@langchain/langgraph-sdk": "^0.0.104", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@react-google-maps/api": "^2.19.3", "@react-spring/web": "^10.0.1", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-query": "^5.56.2", "@types/lodash": "^4.17.16", "@types/three": "^0.158.0", "@use-gesture/react": "^10.3.1", "@vis.gl/react-google-maps": "^1.5.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.4.3", "input-otp": "^1.2.4", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.507.0", "next-themes": "^0.3.0", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.5.2", "react-markdown": "^10.0.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "supercluster": "^8.0.1", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "use-supercluster": "^1.2.0", "uuid": "^9.0.1", "vaul": "^0.9.3", "voice-stream": "^1.0.1", "zod": "^3.23.8", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@shadcn/ui": "^0.0.4", "@tailwindcss/typography": "^0.5.16", "@types/google.maps": "^3.58.1", "@types/node": "^22.13.9", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/uuid": "^9.0.8", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.20", "eslint": "^9.21.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "lovable-tagger": "^1.1.7", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.2", "typescript-eslint": "^8.26.0", "vite": "^5.4.14", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-pwa": "^1.0.0", "vitest": "^3.2.3"}}