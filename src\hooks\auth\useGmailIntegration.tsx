import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "./useAuth";
import { toast } from "@/hooks/use-toast";

interface GmailIntegration {
  id: string;
  provider: string;
  integration_id: string;
  is_active: boolean;
  metadata?: {
    email?: string;
    scopes?: string[];
  };
  created_at: string;
  updated_at: string;
}

interface EmailData {
  id: string;
  sender: string;
  subject: string;
  preview: string;
  time: string;
  isRead: boolean;
  isStarred: boolean;
  body?: string;
}

export const useGmailIntegration = () => {
  const { user, isAuthenticated } = useAuth();
  const [integration, setIntegration] = useState<GmailIntegration | null>(null);
  const [emails, setEmails] = useState<EmailData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  // Call Composio Edge Function
  const callComposioFunction = async (action: string, params: any = {}) => {
    const { data, error } = await supabase.functions.invoke('composio-gmail', {
      body: { action, userId: user?.id, ...params }
    });

    if (error) throw error;
    if (!data.success) throw new Error(data.error);
    return data.data;
  };

  // Check if user has Gmail integration
  const checkIntegration = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("integration_authorizations")
        .select("*")
        .eq("user_id", user.id)
        .eq("provider", "gmail")
        .eq("is_active", true)
        .single();

      if (error && error.code !== "PGRST116") {
        console.error("Error checking integration:", error);
        return;
      }

      setIntegration(data);
    } catch (error) {
      console.error("Error checking integration:", error);
    }
  };

  // Connect Gmail account using Composio Edge Function
  const connectGmail = async () => {
    if (!isAuthenticated) {
      toast({
        title: "Errore",
        description: "Devi essere autenticato per connettere Gmail",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsConnecting(true);
      console.log("Connecting Gmail...");
      
      const response = await callComposioFunction('connect', {
        authConfigId: "ac_UvsrdfxJ2h2X" // Gmail auth config ID
      });

      console.log("Initiate OAuth response:", response);
      
      // Store connection details for callback
      sessionStorage.setItem("composio_connection_id", response.connectionId);
      
      // Open OAuth window
      window.open(response.redirectUrl, '_blank');
      
      toast({
        title: "Connessione Gmail",
        description: "Apri la finestra popup per completare l'autenticazione",
      });
      
    } catch (error: any) {
      console.error("Error connecting Gmail:", error);
      toast({
        title: "Errore",
        description: "Impossibile avviare la connessione Gmail",
        variant: "destructive",
      });
    } finally {
      setIsConnecting(false);
    }
  };

  // Handle OAuth callback
  const handleCallback = async (code: string, state: string) => {
    if (!isAuthenticated) return false;

    try {
      setIsLoading(true);
      
      const connectionId = sessionStorage.getItem("composio_connection_id");
      
      console.log("OAuth callback completed for connection:", connectionId);

      // Call edge function to handle callback
      await callComposioFunction('callback', {
        connectionId,
        code,
        state
      });

      // Clean up session storage
      sessionStorage.removeItem("composio_connection_id");

      // Refresh integration status
      await checkIntegration();
      
      toast({
        title: "Successo",
        description: "Gmail connesso con successo!",
      });

      return true;
      
    } catch (error: any) {
      console.error("Error handling OAuth callback:", error);
      toast({
        title: "Errore",
        description: "Errore durante la connessione Gmail",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch emails from Gmail using Composio Edge Function
  const fetchEmails = async () => {
    if (!integration) return;

    try {
      setIsLoading(true);
      
      const response = await callComposioFunction('fetch_emails', {
        connectionId: integration.integration_id
      });

      console.log("Emails response:", response);

      setEmails(response.emails || []);
      
    } catch (error: any) {
      console.error("Error fetching emails:", error);
      toast({
        title: "Errore",
        description: "Impossibile recuperare le email",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Send email via Gmail using Composio Edge Function
  const sendEmail = async (to: string, subject: string, body: string) => {
    if (!integration) {
      toast({
        title: "Errore",
        description: "Gmail non è connesso",
        variant: "destructive",
      });
      return false;
    }

    try {
      setIsLoading(true);
      
      const response = await callComposioFunction('send_email', {
        connectionId: integration.integration_id,
        to,
        subject,
        body
      });

      console.log("Send email response:", response);

      toast({
        title: "Successo",
        description: "Email inviata con successo!",
      });
      return true;
    } catch (error: any) {
      console.error("Error sending email:", error);
      toast({
        title: "Errore",
        description: "Impossibile inviare l'email",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Disconnect Gmail
  const disconnectGmail = async () => {
    if (!integration) return;

    try {
      await callComposioFunction('disconnect', {
        connectionId: integration.integration_id
      });

      setIntegration(null);
      setEmails([]);

      toast({
        title: "Successo",
        description: "Gmail disconnesso con successo",
      });
    } catch (error) {
      console.error("Error disconnecting Gmail:", error);
      toast({
        title: "Errore",
        description: "Impossibile disconnettere Gmail",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      checkIntegration();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (integration) {
      fetchEmails();
    }
  }, [integration]);

  return {
    integration,
    emails,
    isLoading,
    isConnecting,
    isConnected: !!integration,
    connectGmail,
    disconnectGmail,
    handleCallback,
    fetchEmails,
    sendEmail,
  };
};