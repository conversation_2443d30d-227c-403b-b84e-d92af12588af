import { useState, useEffect, useCallback } from 'react';

interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  tag?: string;
  data?: any;
  actions?: NotificationAction[];
  silent?: boolean;
  vibrate?: number[];
  requireInteraction?: boolean;
  timestamp?: number;
}

interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

interface PWANotificationState {
  permission: NotificationPermission;
  isSupported: boolean;
  isServiceWorkerReady: boolean;
  subscription: PushSubscription | null;
}

export const usePWANotifications = () => {
  const [state, setState] = useState<PWANotificationState>({
    permission: 'default',
    isSupported: 'Notification' in window,
    isServiceWorkerReady: false,
    subscription: null,
  });

  const [pendingNotifications, setPendingNotifications] = useState<NotificationOptions[]>([]);

  // Check initial state
  useEffect(() => {
    if ('Notification' in window) {
      setState(prev => ({
        ...prev,
        permission: Notification.permission,
      }));
    }

    // Check service worker registration
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then(() => {
        setState(prev => ({
          ...prev,
          isServiceWorkerReady: true,
        }));
      });
    }
  }, []);

  // Request notification permission
  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
    if (!state.isSupported) {
      throw new Error('Notifications are not supported');
    }

    try {
      const permission = await Notification.requestPermission();
      setState(prev => ({ ...prev, permission }));
      return permission;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      throw error;
    }
  }, [state.isSupported]);

  // Show local notification
  const showNotification = useCallback(async (options: NotificationOptions) => {
    if (!state.isSupported) {
      console.warn('Notifications are not supported');
      return null;
    }

    if (state.permission !== 'granted') {
      // Queue notification for later if permission is not granted
      setPendingNotifications(prev => [...prev, options]);
      return null;
    }

    try {
      const notificationOptions: NotificationOptions = {
        ...options,
        icon: options.icon || '/icon-192x192.png',
        badge: options.badge || '/icon-192x192.png',
        timestamp: options.timestamp || Date.now(),
        vibrate: options.vibrate || [200, 100, 200],
      };

      let notification: Notification;

      if (state.isServiceWorkerReady && 'serviceWorker' in navigator) {
        // Use service worker for better reliability
        const registration = await navigator.serviceWorker.ready;
        await registration.showNotification(options.title, notificationOptions);
        notification = new Notification(options.title, notificationOptions);
      } else {
        // Fallback to regular notification
        notification = new Notification(options.title, notificationOptions);
      }

      // Auto-close after 5 seconds if not requiring interaction
      if (!options.requireInteraction) {
        setTimeout(() => {
          notification.close();
        }, 5000);
      }

      return notification;
    } catch (error) {
      console.error('Error showing notification:', error);
      return null;
    }
  }, [state.isSupported, state.permission, state.isServiceWorkerReady]);

  // Show pending notifications after permission is granted
  useEffect(() => {
    if (state.permission === 'granted' && pendingNotifications.length > 0) {
      pendingNotifications.forEach(notification => {
        showNotification(notification);
      });
      setPendingNotifications([]);
    }
  }, [state.permission, pendingNotifications, showNotification]);

  // Subscribe to push notifications
  const subscribeToPush = useCallback(async (vapidPublicKey: string) => {
    if (!state.isServiceWorkerReady) {
      throw new Error('Service worker is not ready');
    }

    if (state.permission !== 'granted') {
      throw new Error('Notification permission not granted');
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(vapidPublicKey),
      });

      setState(prev => ({ ...prev, subscription }));
      return subscription;
    } catch (error) {
      console.error('Error subscribing to push notifications:', error);
      throw error;
    }
  }, [state.isServiceWorkerReady, state.permission]);

  // Unsubscribe from push notifications
  const unsubscribeFromPush = useCallback(async () => {
    if (!state.subscription) {
      return true;
    }

    try {
      const success = await state.subscription.unsubscribe();
      if (success) {
        setState(prev => ({ ...prev, subscription: null }));
      }
      return success;
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error);
      return false;
    }
  }, [state.subscription]);

  // Predefined notification types for the chat app
  const showChatNotification = useCallback((message: string, sender: string = 'AI Assistant') => {
    return showNotification({
      title: `New message from ${sender}`,
      body: message,
      tag: 'chat-message',
      actions: [
        { action: 'reply', title: 'Reply', icon: '/icons/reply.png' },
        { action: 'view', title: 'View', icon: '/icons/view.png' },
      ],
      data: { type: 'chat', sender, message },
    });
  }, [showNotification]);

  const showSystemNotification = useCallback((title: string, message: string) => {
    return showNotification({
      title,
      body: message,
      tag: 'system',
      silent: true,
      data: { type: 'system' },
    });
  }, [showNotification]);

  const showOfflineNotification = useCallback(() => {
    return showNotification({
      title: 'You\'re offline',
      body: 'Messages will be sent when you\'re back online',
      tag: 'offline',
      icon: '/icons/offline.png',
      data: { type: 'offline' },
    });
  }, [showNotification]);

  const showOnlineNotification = useCallback((pendingCount: number = 0) => {
    const body = pendingCount > 0 
      ? `You're back online! Sending ${pendingCount} pending messages...`
      : 'You\'re back online!';

    return showNotification({
      title: 'Connection restored',
      body,
      tag: 'online',
      icon: '/icons/online.png',
      data: { type: 'online', pendingCount },
    });
  }, [showNotification]);

  // Clear all notifications with a specific tag
  const clearNotifications = useCallback(async (tag?: string) => {
    if (!state.isServiceWorkerReady) return;

    try {
      const registration = await navigator.serviceWorker.ready;
      const notifications = await registration.getNotifications({ tag });
      
      notifications.forEach(notification => {
        notification.close();
      });
    } catch (error) {
      console.error('Error clearing notifications:', error);
    }
  }, [state.isServiceWorkerReady]);

  // Get active notifications
  const getActiveNotifications = useCallback(async (tag?: string) => {
    if (!state.isServiceWorkerReady) return [];

    try {
      const registration = await navigator.serviceWorker.ready;
      return await registration.getNotifications({ tag });
    } catch (error) {
      console.error('Error getting active notifications:', error);
      return [];
    }
  }, [state.isServiceWorkerReady]);

  // Check if notifications are enabled and working
  const testNotification = useCallback(async () => {
    try {
      const notification = await showNotification({
        title: 'Test Notification',
        body: 'Notifications are working correctly!',
        tag: 'test',
        requireInteraction: false,
      });
      
      return !!notification;
    } catch (error) {
      console.error('Test notification failed:', error);
      return false;
    }
  }, [showNotification]);

  return {
    // State
    ...state,
    pendingNotifications,

    // Permission management
    requestPermission,

    // Local notifications
    showNotification,
    clearNotifications,
    getActiveNotifications,

    // Push notifications
    subscribeToPush,
    unsubscribeFromPush,

    // Predefined notifications
    showChatNotification,
    showSystemNotification,
    showOfflineNotification,
    showOnlineNotification,

    // Utilities
    testNotification,
  };
};

// Helper function to convert VAPID key
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}
