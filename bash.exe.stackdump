Stack trace:
Frame         Function      Args
0007FFFFA930  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF9830) msys-2.0.dll+0x2118E
0007FFFFA930  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFA930  0002100469F2 (00021028DF99, 0007FFFFA7E8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFA930  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFA930  00021006A545 (0007FFFFA940, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFA940, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA90180000 ntdll.dll
7FFA8F1F0000 KERNEL32.DLL
7FFA8D660000 KERNELBASE.dll
7FFA8EE90000 USER32.dll
7FFA8DCD0000 win32u.dll
000210040000 msys-2.0.dll
7FFA8F440000 GDI32.dll
7FFA8DAE0000 gdi32full.dll
7FFA8DC20000 msvcp_win.dll
7FFA8D2D0000 ucrtbase.dll
7FFA8EDD0000 advapi32.dll
7FFA8E520000 msvcrt.dll
7FFA8FFA0000 sechost.dll
7FFA8F310000 RPCRT4.dll
7FFA8C8D0000 CRYPTBASE.DLL
7FFA8DD00000 bcryptPrimitives.dll
7FFA8E300000 IMM32.DLL
